using Irix.Entities;
using Irix.Persistence.Contract;
using Irix.Service.Contract;
using Irix.ServiceAdapters.Irix.Entities;
using Logger.Contract;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Re_Pricer.Identity;
using Repricer.Cache;
using RePricer.Util;
using Swashbuckle.AspNetCore.Annotations;
using System.Security.Claims;
using Constant = RePricer.Constants.ServiceConstants;
using ILogger = Logger.Contract.ILogger;

namespace Re_Pricer.Controllers
{
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class AdminController : ApiBaseController
    {
        private readonly UserManager<ApplicationUser> _userManager;
        public readonly ILogger _log;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IClientServices _clientServices;
        private readonly IClientPersistance _clientPersistance;
        private readonly IReservationPersistence _reservationPersistance;
        private readonly IMasterService _masterService;
        private readonly IExchangeRateService _exchangeRateService;
        private readonly IMasterPersistence _masterPersistence;
        private readonly string _className;
        private readonly ISearchServicerHelper _searchServicerHelper;
        private readonly ILoggerMongoDb _loggerMongoDb;
        private readonly IJobService _jobService;
        private readonly IConfigurationService _configurationService;
        private readonly IReservationService _reservationService;

        public AdminController(UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager, IClientServices clientServices, IClientPersistance clientPersistance, IMasterPersistence masterPersistence,
            IReservationPersistence reservationPersistance,
            IMasterService masterService
            , ILogger log
            , IExchangeRateService exchangeRateService
            , ISearchServicerHelper searchServicerHelper
            , ILoggerMongoDb loggerMongoDb
            , IJobService jobService
            , IConfigurationService configurationService
            , IReservationService reservationService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _clientServices = clientServices;
            _clientPersistance = clientPersistance;
            _masterPersistence = masterPersistence;
            _reservationPersistance = reservationPersistance;
            _masterService = masterService;
            _log = log;
            _className = nameof(AdminController);
            _exchangeRateService = exchangeRateService;
            _searchServicerHelper = searchServicerHelper;
            _loggerMongoDb = loggerMongoDb;
            _jobService = jobService;
            _configurationService = configurationService;
            _reservationService = reservationService;
        }

        /// <summary>
        /// Create User
        /// </summary>
        /// <remarks>
        /// POST /api/Admin/CreateUser
        ///
        /// Example request:
        /// {
        ///     "confirmPassword": "Active@123",
        ///     "email": "<EMAIL>",
        ///     "password": "Active@123",
        ///     "userName": "Test1",
        ///     "roles": [
        ///         "user"
        ///     ]
        /// }
        /// </remarks>
        /// <param name="userModel">The user model containing user information</param>
        /// <returns>Returns the newly created user</returns>
        /// <returns></returns>
        [HttpPost("CreateUser")]
        [SwaggerResponse(200, "Users created successfully", typeof(CreateResponse))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin")]
        public async Task<IActionResult> CreateUser(SignUpUserModel userModel)
        {
            try
            {
                var userdata = GetRolesAndRepricerUserId();
                if (userdata.RepricerUserId == 0)
                {
                    if (userModel.RepricerId != 0)
                    {
                        var result = (_clientPersistance.LoadRePricerDetail(userModel.RepricerId)).GetAwaiter().GetResult();
                        if (result.RepricerUserName == null)
                        {
                            return BadRequest("Please Contact Administrator no Repricer Found");
                        }
                    }
                }

                if (userdata != null)
                {
                    userModel.Roles = userModel.Roles.Where(role => !string.IsNullOrEmpty(role)).ToList();

                    if (userModel.Roles != null && userModel.Roles.Count == 0)
                    {
                        return BadRequest("No Roles assigned to User.");
                    }

                    if (userdata.Roles.Contains("SuperAdmin") && userModel.Roles.Contains("User"))
                    {
                        return BadRequest("SuperAdmin cannot create a User for any Admin.");
                    }

                    if (userdata.RepricerUserId == 0 && userdata.Roles.Any(role => role != "SuperAdmin"))
                    {
                        return BadRequest($"Please Contact Administrator");
                    }
                }

                if (ModelState.IsValid)
                {
                    if (userModel.Roles != null && userModel.Roles.Any(role => role.Equals("SuperAdmin", StringComparison.OrdinalIgnoreCase)))
                    {
                        return BadRequest(new
                        {
                            Errors = "Cannot Create this user as it is assigned 'superadmin' role."
                        });
                    }
                    var existingUserByUserName = await _userManager.FindByNameAsync(userModel.UserName);
                    if (existingUserByUserName != null)
                    {
                        return BadRequest("A user with this username already exists.");
                    }

                    var existuser = await _userManager.FindByEmailAsync(userModel.Email);
                    if (existuser != null)
                    {
                        return BadRequest("A user with this email already exists.");
                    }

                    var user = new ApplicationUser()
                    {
                        UserName = userModel.UserName,
                        Email = userModel.Email,
                        EmailConfirmed = true
                    };

                    var result = await _userManager.CreateAsync(user, userModel.Password);
                    var UserDetail = await _userManager.FindByNameAsync(userModel.UserName);
                    if (result.Succeeded)
                    {
                        if (userdata != null)
                        {
                            if (userdata.Roles.Any(role => role == "SuperAdmin"))
                            {
                                if (userModel.RepricerId != 0)
                                {
                                    await _masterPersistence.InsertUserMapping(userModel.RepricerId, UserDetail.Id);
                                }
                                else
                                {
                                    return BadRequest($"First Add RePricer Configuration For this Admin User");
                                }
                            }
                            else if (userdata.Roles.Any(role => role == "Admin"))
                            {
                                if (userdata.RepricerUserId != 0)
                                {
                                    await _masterPersistence.InsertUserMapping(userdata.RepricerUserId, UserDetail.Id);
                                }
                                else
                                {
                                    return BadRequest($"First Add RePricer Configuration For this Admin User");
                                }
                            }
                        }
                    }
                    if (result.Succeeded)
                    {
                        foreach (var role in userModel.Roles)
                        {
                            if (!string.IsNullOrEmpty(role))
                            {
                                if (role.Equals("SuperAdmin", StringComparison.OrdinalIgnoreCase))
                                {
                                    return BadRequest(new
                                    {
                                        Errors = "Cannot assign the 'superadmin' role."
                                    });
                                }

                                var roleExists = await _roleManager.RoleExistsAsync(role);
                                if (roleExists)
                                {
                                    await _userManager.AddToRoleAsync(user, role);
                                }
                                else
                                {
                                    ModelState.AddModelError(string.Empty, $"The role '{role}' does not exist.");
                                    return BadRequest(ModelState);
                                }
                            }
                        }
                        try
                        {
                            var newusermodel = userModel;

                            newusermodel.Password = null;
                            newusermodel.ConfirmPassword = null;

                            var requestJson = JsonConvert.SerializeObject(newusermodel);

                            _loggerMongoDb.ChangeStatelogs(userModel.RepricerId, requestJson?.ToString(), "200", MethodType.UserMapping.ToString(), requestJson?.ToString(), null, userdata?.RepricerUserName);
                        }
                        catch (Exception ex)
                        {
                        }
                        CacheRefreshAllUser();

                        return Ok("User created successfully.");
                    }
                    else
                    {
                        var errorMessages = result.Errors
                            .Select(e => e.Description)
                            .ToList();

                        var customErrorMessages = new List<string>();

                        if (errorMessages.Any(msg => msg.Contains("Password") && msg.ToLower().Contains("non alphanumeric")))
                        {
                            customErrorMessages.Add("Password must contain at least one special character.");
                        }

                        // Add any other specific error handling logic here if needed

                        // Add all other error messages
                        customErrorMessages.AddRange(errorMessages.Where(msg =>
                            !msg.Contains("Password") || !msg.ToLower().Contains("non alphanumeric")));

                        var finalErrorMessage = string.Join("; ", customErrorMessages);

                        return BadRequest(new
                        {
                            Errors = finalErrorMessage
                        });
                    }
                }
                else
                {
                    return BadRequest(ModelState);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(CreateUser)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// UpdateUser
        /// </summary>
        /// <param name="userModel"> </param>
        /// <returns> </returns>
        [HttpPut("UpdateUser")]
        [SwaggerResponse(200, "Role created successfully", typeof(EditResponse))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin")]
        public async Task<IActionResult> UpdateUser(UpdateUserModel userModel)
        {
            try
            {
                var userdata = GetRolesAndRepricerUserId();

                if (ModelState.IsValid)
                {
                    var user = await _userManager.FindByNameAsync(userModel.UserName);

                    if (user == null)
                    {
                        return NotFound("User not found.");
                    }

                    if (userModel.Email != null)
                    {
                        var existingUserWithEmail = await _userManager.FindByEmailAsync(userModel.Email);
                        if (existingUserWithEmail != null && existingUserWithEmail.Id != user.Id)
                        {
                            return BadRequest("Email is already in use by another user.");
                        }
                        user.Email = userModel.Email;
                        var result = await _userManager.UpdateAsync(user);

                        if (!result.Succeeded)
                        {
                            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                            return BadRequest($"Failed to update email: {errors}");
                        }
                    }

                    if (userModel.Password != null)
                    {
                        if (userModel.Password == userModel.ConfirmPassword)
                        {
                            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                            var updatepass = await _userManager.ResetPasswordAsync(user, token, userModel.Password);
                            if (!updatepass.Succeeded)
                            {
                                var errors = string.Join(", ", updatepass.Errors.Select(e => e.Description));
                                return BadRequest($"Failed to update password: {errors}");
                            }
                        }
                        else
                        {
                            return BadRequest("To Change the password ,Password and confirm password shoould be same.");
                        }
                    }
                    if (userModel.Roles != null && userModel.Roles.Count > 0)
                    {
                        userModel.Roles = userModel.Roles.Where(role => !string.IsNullOrEmpty(role)).ToList();
                        if (userModel.Roles != null && userModel.Roles.Count > 0)
                        {
                            var userRoles = await _userManager.GetRolesAsync(user);
                            if (userModel.Roles != null && userModel.Roles.Any(role => role.Equals("SuperAdmin", StringComparison.OrdinalIgnoreCase)))
                            {
                                return BadRequest(new
                                {
                                    Errors = "Cannot assign the 'superadmin' role."
                                });
                            }
                            foreach (var role in userRoles)
                            {
                                await _userManager.RemoveFromRoleAsync(user, role);
                            }

                            foreach (var role in userModel.Roles)
                            {
                                if (!string.IsNullOrEmpty(role))
                                {
                                    var roleExists = await _roleManager.RoleExistsAsync(role);
                                    if (roleExists)
                                    {
                                        await _userManager.AddToRoleAsync(user, role);
                                    }
                                    else
                                    {
                                        ModelState.AddModelError(string.Empty, $"The role '{role}' does not exist.");
                                        return BadRequest(ModelState);
                                    }
                                }
                            }
                        }
                    }
                    CacheRefreshAllUser();

                    return Ok(new
                    {
                        Message = "Updated successfully"
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        Errors = "Failed to update user."
                    });
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateUser)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// Get User By Id
        /// </summary>
        /// <param name="isCacheRefresh"></param>
        /// <param name="userId"> </param>
        /// <returns> </returns>
        [HttpGet("ViewAllUsers")]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult ViewAllUsers(bool isCacheRefresh)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if (usersData.Roles.Any(role => role?.ToLowerInvariant() == "SuperAdmin".ToLowerInvariant()))
                {
                    usersData.RepricerUserId = 0;
                }
                if (usersData.Roles.Any(role => role?.ToLowerInvariant() == "Admin".ToLowerInvariant()) && usersData.RepricerUserId == 0)
                {
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }

                var helper = new IdentityModelHelper(_userManager, _clientPersistance, _masterPersistence);

                var usersList = helper.CacheUsersList(usersData, !isCacheRefresh);

                return Ok(usersList);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(ViewAllUsers)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, $"An error occurred while processing the request. Error Message = {ex.Message}");
            }
        }

        /// <summary>
        /// Creates a new RePricer client configuration
        /// </summary>
        /// <remarks>
        /// This endpoint creates a new RePricer client configuration with the provided details.
        ///
        /// Sample request:
        /// ```json
        /// {
        ///   "repricerUserName": "ClientName",
        ///   "adminUrl": "https://admin.example.com",
        ///   "adminUserId": "adminuser",
        ///   "adminPassword": "password123",
        ///   "resellerUrl": "https://reseller.example.com",
        ///   "resellerUserId": "reselleruser",
        ///   "resellerPassword": "password123",
        ///   "adminApiScope": "admin_api",
        ///   "resellerApiScope": "reseller_api",
        ///   "isActive": true,
        ///   "isJobsEnable": true,
        ///   "optimizationType": 1,
        ///   "extraClientDetail": {
        ///     "travelDaysMaxSearchInDays": 45,
        ///     "travelDaysMinSearchInDays": 30,
        ///     "maxNumberOfTimesOptimization": 5,
        ///     "clientConfig_DaysDifferenceInPreBookCreation": 3,
        ///     "priceDifferenceValue": 20.0,
        ///     "priceDifferencePercentage": 0.0,
        ///     "isUsePercentage": false,
        ///     "currency": "EUR",
        ///     "priceDifferenceValueAllowedBufferPercentage": 0,
        ///     "isCheckCrossSupperBeforeOptimization": false
        ///   }
        /// }
        /// ```
        /// </remarks>
        /// <param name="rePricerDetail">The RePricer client configuration details</param>
        /// <returns>
        /// 200 OK - Returns the created RePricer configuration with its assigned ID
        /// 400 Bad Request - If the request is invalid or configuration creation fails
        /// </returns>
        [Route("CreateRepricer")]
        [HttpPost]
        [SwaggerResponse(200, "RePricer configuration created successfully", typeof(CreateResponse))]
        [SwaggerResponse(400, "Bad request - Invalid input or configuration creation failed", typeof(ErrorResponse))]
        public IActionResult CreateRepricer(RePricerDetail rePricerDetail)
        {
            try
            {
                rePricerDetail = ValidateRePricerDetail(rePricerDetail);
                var repricerid = _clientServices.InsertRePricerDetail(rePricerDetail);
                rePricerDetail.RepricerUserID = repricerid;
                CacheRefreshAllUser();
                string response;
                if (repricerid > 0)
                {
                    var operation = repricerid > 0 ? " Updated" : " Inserted";
                    response = rePricerDetail.RepricerUserName + $" {operation} successfully";
                    var data = default(RePricerDetail);
                    var rePricerUserId = repricerid > 0 ? repricerid : (rePricerDetail.RepricerUserID > 0 ? rePricerDetail.RepricerUserID : repricerid);
                    if (rePricerUserId > 0)
                    {
                        _clientServices.GetClientEmail(rePricerUserId, false);

                        data = (_clientPersistance.LoadRePricerDetail(repricerid, null, true)).GetAwaiter().GetResult();
                    }

                    return Ok(new
                    {
                        Message = response,
                        data = data
                    });
                }
                else
                {
                    response = rePricerDetail.RepricerUserName + " not inserted";
                    return BadRequest(new
                    {
                        Errors = response
                    });
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(ViewAllUsers)
                };
                _log.Error(irixErrorEntity, ex);

                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        /// <summary>
        /// Updates an existing RePricer client configuration
        /// </summary>
        /// <remarks>
        /// This endpoint updates an existing RePricer client configuration with the provided details.
        /// The RepricerUserID must be specified in the request body.
        ///
        /// Sample request:
        /// ```json
        /// {
        ///   "repricerUserID": 1,
        ///   "repricerUserName": "ClientName",
        ///   "adminUrl": "https://admin.example.com",
        ///   "adminUserId": "adminuser",
        ///   "adminPassword": "password123",
        ///   "resellerUrl": "https://reseller.example.com",
        ///   "resellerUserId": "reselleruser",
        ///   "resellerPassword": "password123",
        ///   "adminApiScope": "admin_api",
        ///   "resellerApiScope": "reseller_api",
        ///   "isActive": true,
        ///   "isJobsEnable": true,
        ///   "optimizationType": 1,
        ///   "extraClientDetail": {
        ///     "travelDaysMaxSearchInDays": 45,
        ///     "travelDaysMinSearchInDays": 30,
        ///     "maxNumberOfTimesOptimization": 5,
        ///     "clientConfig_DaysDifferenceInPreBookCreation": 3,
        ///     "priceDifferenceValue": 20.0,
        ///     "priceDifferencePercentage": 0.0,
        ///     "isUsePercentage": false,
        ///     "currency": "EUR",
        ///     "priceDifferenceValueAllowedBufferPercentage": 0,
        ///     "isCheckCrossSupperBeforeOptimization": false
        ///   }
        /// }
        /// ```
        /// </remarks>
        /// <param name="rePricerDetail">The updated RePricer client configuration details</param>
        /// <returns>
        /// 200 OK - Returns the updated RePricer configuration
        /// 400 Bad Request - If the request is invalid or configuration update fails
        /// </returns>
        [HttpPut("UpdateRepricer")]
        [SwaggerResponse(200, "RePricer configuration updated successfully", typeof(EditResponse))]
        [SwaggerResponse(400, "Bad request - Invalid input or configuration update failed", typeof(ErrorResponse))]
        public IActionResult UpdateRepricer(RePricerDetail rePricerDetail)
        {
            string response;
            var usersData = GetRolesAndRepricerUserId();

            try
            {
                if (rePricerDetail.RepricerUserID == 0 || rePricerDetail.RepricerUserID == null)
                {
                    response = "RepricerUserID missing in update request.";
                    return BadRequest(new
                    {
                        Errors = response
                    });
                }
                rePricerDetail = ValidateRePricerDetail(rePricerDetail);
                var oldstate = (_clientPersistance.LoadRePricerDetail(rePricerDetail.RepricerUserID, null, true)).GetAwaiter().GetResult();

                if (rePricerDetail.RepricerUserID != 0)
                {
                    rePricerDetail.ModifiedBy = usersData.RepricerUserName;
                    var config = _configurationService.InsertConfiguration(rePricerDetail.RepricerUserID);
                    var configResult = _configurationService.ConvertToConfigurationResult(rePricerDetail.RepricerUserID, config);
                    if (config?.Message?.ToLower() == "Unable to fetch Irix Configuration. Please check all permissions!".ToLower() || config == null)
                    {
                        configResult = null;
                    }
                    var result = _clientServices.InsertRePricerDetail(rePricerDetail, configResult);
                    if (result > 0)
                    {
                        response = rePricerDetail.RepricerUserName + " details updated successfully.";
                        var data = default(RePricerDetail);
                        var rePricerUserId = rePricerDetail.RepricerUserID;
                        Task.Run(() => CacheRefreshAllUser()).ConfigureAwait(false);
                        if (rePricerUserId > 0)
                        {
                            _clientServices.GetClientEmail(rePricerUserId, false);
                            data = (_clientPersistance.LoadRePricerDetail(rePricerUserId, null, true)).GetAwaiter().GetResult();
                        }
                        try
                        {
                            var repricerdetailjson = JsonConvert.SerializeObject(rePricerDetail);
                            var dataJson = JsonConvert.SerializeObject(data);
                            var oldstateJson = JsonConvert.SerializeObject(oldstate);

                            Task.Run(() =>
                            {
                                try
                                {
                                    var helper = new IdentityModelHelper(_userManager, _clientPersistance, _masterPersistence);

                                    var usersList = helper.CacheUsersList(usersData, false);
                                }
                                catch (Exception ex)
                                {
                                }
                            });
                            //_loggerMongoDb.ChangeStatelogs(rePricerUserId, repricerdetailjson?.ToString(), response, MethodType.Configuration.ToString(), dataJson?.ToString(), oldstateJson?.ToString(), usersData.RepricerUserName);
                        }
                        catch (Exception ex)
                        {
                        }
                        return Ok(new
                        {
                            Message = response,
                            data = data
                        });
                    }
                }

                response = rePricerDetail.RepricerUserName + " details update failed.";

                return BadRequest(new
                {
                    Errors = response
                });
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateRepricer)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        /// <summary>
        /// Retrieves a RePricer client configuration by ID
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves the Complete configuration details for a specific RePricer client.
        ///
        /// The endpoint requires authentication and is accessible to users with Admin, SuperAdmin, or User roles.
        /// - SuperAdmin users can access any RePricer configuration
        /// - Admin and User roles can only access their assigned RePricer configuration
        /// </remarks>
        /// <param name="RePricerId">The ID of the RePricer configuration to retrieve</param>
        /// <returns>
        /// 200 OK - Returns the Complete RePricer configuration
        /// 400 Bad Request - If the RePricerId is invalid or the user doesn't have access to the requested configuration
        /// 500 Internal Server Error - If an unexpected error occurs during processing
        /// </returns>
        [Route("GetRepricer/{RePricerId}")]
        [HttpGet]
        [SwaggerResponse(200, "RePricer configuration details retrieved successfully", typeof(RePricerDetail))]
        [SwaggerResponse(400, "Bad request - Invalid ID or insufficient permissions", typeof(ErrorResponse))]
        [SwaggerResponse(500, "Internal server error", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetRepricer(int RePricerId)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    _clientServices.GetClientEmail(usersData.RepricerUserId, false);
                    var result = _clientPersistance.LoadRePricerDetail(usersData.RepricerUserId, null, true)?.GetAwaiter().GetResult();
                    Task.Run(
                                () => _clientServices.GetClientScheduler(true)?.FirstOrDefault(x => x.RepricerId == usersData.RepricerUserId)
                            ).ConfigureAwait(false);
                    return GetResponseWithActionResult(result);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetRepricer)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// Deletes a RePricer client configuration
        /// </summary>
        /// <remarks>
        /// This endpoint permanently deletes a RePricer client configuration from the system.
        ///
        /// **Important Notes:**
        /// - This operation cannot be undone
        /// - Only users with the SuperAdmin role can delete RePricer configurations
        /// - All associated data (including scheduling configurations) will be removed
        /// </remarks>
        /// <param name="RePricerId">The ID of the RePricer configuration to delete</param>
        /// <returns>
        /// 200 OK - If the configuration was successfully deleted
        /// 400 Bad Request - If the deletion failed
        /// 500 Internal Server Error - If an unexpected error occurs during processing
        /// </returns>
        [Route("DeleteRepricer/{RePricerId}")]
        [HttpDelete]
        [SwaggerResponse(200, "RePricer configuration deleted successfully", typeof(string))]
        [SwaggerResponse(400, "Bad request - Invalid ID or deletion failed", typeof(ErrorResponse))]
        [SwaggerResponse(500, "Internal server error", typeof(ErrorResponse))]
        [Authorize(Roles = "SuperAdmin")]
        public IActionResult DeleteRepricer(int RePricerId)
        {
            try
            {
                var result = _clientPersistance.DeleteClientDataAsync(RePricerId);
                string response;
                if (result > 0)
                {
                    response = "deleted successfully";
                }
                else
                {
                    response = "not deleted";
                }
                return GetResponseWithActionResult(response);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(DeleteRepricer)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// To Unlock User
        /// </summary>
        /// <param name="UserName"></param>
        /// <returns></returns>
        [HttpPost("UnlockUser/{UserName}")]
        [Authorize(Roles = "Admin,SuperAdmin")]
        public async Task<IActionResult> UnlockUser(string UserName)
        {
            try
            {
                if (string.IsNullOrEmpty(UserName))
                {
                    return BadRequest();
                }
                var user = await _userManager.FindByNameAsync(UserName);

                if (user == null)
                {
                    return NotFound();
                }

                var helper = new IdentityModelHelper(_userManager, _clientPersistance, _masterPersistence);
                var model = await helper.GetUserDetailsViewModel(user.Id);

                try
                {
                    var result = await _userManager.SetLockoutEndDateAsync(user, null);
                    if (!result.Succeeded)
                    {
                        return BadRequest(result.Errors);
                    }
                    else
                    {
                        model = await helper.GetUserDetailsViewModel(user.Id);
                    }
                }
                catch (Exception ex)
                {
                    var irixErrorEntity = new IrixErrorEntity
                    {
                        ClassName = _className,
                        MethodName = nameof(UnlockUser)
                    };
                    _log.Error(irixErrorEntity, ex);
                    return BadRequest(new
                    {
                        Errors = ex.Message
                    });
                }
                return Ok("Unlocked");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UnlockUser)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// Delete User Permanantly
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [Route("DeleteUser/{userId}")]
        [HttpDelete]
        [Authorize(Roles = "Admin,SuperAdmin")]
        public async Task<IActionResult> DeleteUser(string userId)
        {
            try
            {
                var userToDelete = await _userManager.FindByIdAsync(userId);

                if (userToDelete == null)
                {
                    return BadRequest("User not found");
                }
                if (userToDelete.UserName == "SuperAdmin")
                {
                    return BadRequest("SuperAdmin Can not be deleted");
                }

                var result = await _userManager.DeleteAsync(userToDelete);
                var userMapping = _clientPersistance.DeleteUserMapping(userId);
                CacheRefreshAllUser();
                if (result.Succeeded)
                {
                    return Ok("User deleted successfully.");
                }
                else
                {
                    foreach (var error in result.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                    return BadRequest(ModelState);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(DeleteUser)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// To Get All Different Supplier by RepricerId
        /// </summary>
        /// <param name="RePricerId"></param>
        /// <returns></returns>
        [Route("GetSupplier/{RePricerId}")]
        [HttpGet]
        [SwaggerResponse(200, "Repricer Detail", typeof(List<SupplierMaster>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetSupplier(int RePricerId)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var result = (_reservationPersistance.GetSupplierMaster(RePricerId)).GetAwaiter().GetResult();
                    return GetResponseWithActionResult(result);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetSupplier)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// To Get Reseller using RepricerId
        /// </summary>
        /// <param name="RePricerId"></param>
        /// <returns></returns>
        [Route("GetReseller/{RePricerId}")]
        [HttpGet]
        [SwaggerResponse(200, "Repricer Detail", typeof(List<ResellerMaster>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetReseller(int RePricerId)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var result = (_reservationPersistance.GetResellerMaster(RePricerId)).GetAwaiter().GetResult();
                    return GetResponseWithActionResult(result);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetReseller)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// To Update Repricer
        /// </summary>
        /// <param name="rePricerDetail"> </param>
        /// <returns> </returns>
        [HttpPut("UpdateSupplier")]
        [SwaggerResponse(200, "Supplier Updated successfully", typeof(EditResponse))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        public IActionResult UpdateSupplier(List<SupplierMaster> supplierMasters)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if (supplierMasters != null && supplierMasters.Count > 0)
                {
                    foreach (var supplierMaster in supplierMasters)
                    {
                        supplierMaster.UpdatedBy = usersData.RepricerUserName;
                        supplierMaster.RepricerId = usersData.RepricerUserId;
                    }
                    try
                    {
                        var key = $"GetSupplierMaster_{usersData.RepricerUserId}";

                        _reservationPersistance.UpdateSupplierMaster(supplierMasters);

                        RedisCacheHelper.KeyDelete(key);
                        GetSupplier(usersData.RepricerUserId);
                        return Ok("Supplier Updated Successfully");
                    }
                    catch (Exception ex)
                    {
                        return BadRequest(ex.Message.ToString());
                    }
                }
                return BadRequest("No data to update");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateSupplier)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        [HttpPut("UpdateReseller")]
        [SwaggerResponse(200, "Reseller Updated successfully", typeof(EditResponse))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        public IActionResult UpdateReseller(List<ResellerMaster> resellerMasters)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if (resellerMasters != null && resellerMasters.Count > 0)
                {
                    foreach (var resellerMaster in resellerMasters)
                    {
                        resellerMaster.UpdatedBy = usersData.RepricerUserName;
                        resellerMaster.RepricerId = usersData.RepricerUserId;
                    }
                    try
                    {
                        var key = $"GetResellerMaster_{usersData.RepricerUserId}";
                        _reservationPersistance.UpdateResellerMaster(resellerMasters);
                        RedisCacheHelper.KeyDelete(key);
                        GetReseller(usersData.RepricerUserId);
                        return Ok("Reseller Updated Successfully");
                    }
                    catch (Exception ex)
                    {
                        return BadRequest(ex.Message.ToString());
                    }
                }
                return BadRequest("No data to update");
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(UpdateReseller)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        /// <summary>
        ///  To save date wise CurrencyExchange in database from API
        /// </summary>
        /// <param name="RepricerIds"></param>
        /// <returns></returns>
        [Route("DumpExchangeRatesReportWise")]
        [HttpGet]
        [Authorize(Roles = "SuperAdmin")]
        public IActionResult DumpExchangeRatesReportWise(string? RepricerIds = null)
        {
            var _methodName = nameof(DumpExchangeRatesReportWise);
            Dictionary<int, Dictionary<string, string>> exchangeRatesDateResult = new Dictionary<int, Dictionary<string, string>>();
            try
            {
                var repricerSchedules = _jobService._0_0_GetRepricerSchedules(RepricerIds);
                repricerSchedules = repricerSchedules.OrderByDescending(x => x.OptimizationType).ThenBy(x => x.RepricerId).ToList();

                if (repricerSchedules?.Any() == false)
                {
                    return BadRequest(Constant.ErrorRepricer);
                }

                var deletedKeys = new List<string>();
                var maxParallelism = Math.Max(Math.Min(repricerSchedules.Count, 5), 1);
                var parallalOptions = new ParallelOptions { MaxDegreeOfParallelism = maxParallelism };

                Parallel.ForEach(repricerSchedules, parallalOptions, repricer =>
                {
                    var repricerId = repricer.RepricerId;

                    var repricerDetails = _clientPersistance.LoadRePricerDetail(repricerId).GetAwaiter().GetResult();
                    var reservationRequestReport = new RepricerReportRequest
                    {
                        RepricerId = repricerId,
                    };

                    try
                    {
                        var reservationReportResponse = _masterService.GetReservationReport(reservationRequestReport);
                        if (reservationReportResponse?.Data?.Any() == true)
                        {
                            var allExchangeRates = _exchangeRateService.LoadExchangeRatesFromIRIXDateWise(repricerDetails, reservationReportResponse);
                            exchangeRatesDateResult.Add(repricerId, allExchangeRates);
                        }
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = _methodName,
                            RePricerId = repricerId,
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                });
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = _methodName
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
            return GetResponseWithActionResult(exchangeRatesDateResult);
        }

        /// <summary>
        /// Get optimized reservation based on different conditions like
        /// 1) Action Taken = y
        /// 2) Reservation status = OK (Confirm) OR XX (CANCELLED)
        /// 3) Check In Date Passed
        /// 4) reportType can be
        /// </summary>
        /// <param name="reservationRequestReport"> </param>
        /// <returns> </returns>
        [Route("GetRepricerReport")]
        [HttpPost]
        [SwaggerResponse(200, "RepricerReportResponse", typeof(RepricerReportResponse))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetRepricerReport(RepricerReportRequest reservationRequestReport)
        {
            try
            {
                var isCached = reservationRequestReport?.IsCached ?? true;
                reservationRequestReport.IsCached = isCached;
                var usersData = GetRolesAndRepricerUserId();
                reservationRequestReport.PageNumber = reservationRequestReport.PageNumber ?? 1;
                reservationRequestReport.PageSize = reservationRequestReport?.PageSize ?? 100;

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (reservationRequestReport.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reservationRequestReport.RepricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (reservationRequestReport?.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reservationRequestReport.RepricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var reservationReportResponse = _masterService.GetReservationReport(reservationRequestReport);

                    // ENHANCEMENT: Get additional prebook options (ranks 2-3) for Active tab
                    if (reservationReportResponse?.Data?.Any() == true)
                    {
                        try
                        {
                            var additionalPrebooks = _masterService.GetAdditionalPrebookOptions(reservationRequestReport);
                            if (additionalPrebooks?.Any() == false)
                            {
                                additionalPrebooks = Common.DeepClone(reservationReportResponse.Data.SelectMany(x=>x.Prebook).to);
                            }
                            if (additionalPrebooks?.Any() == true)
                            {
                                // Combine primary prebooks with additional options
                                reservationReportResponse = _masterService.CombinePrebookOptions(reservationReportResponse, additionalPrebooks, reservationRequestReport.ReportType);
                            }
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(GetRepricerReport)
                            };
                            _log.Error(irixErrorEntity, ex);
                            // Continue with primary prebooks only if additional prebooks fail
                        }
                    }

                    _masterPersistence.ReservationReportSummary(new DashboardSummaryRequest
                    {
                        RepricerId = reservationRequestReport.RepricerId,
                        isCached = isCached
                    });

                    var result = reservationReportResponse?.Data;

                    var prebookRequest = new PrebookRequest
                    {
                        RepricerId = reservationRequestReport.RepricerId,
                        ReservationId = reservationRequestReport.ReservationId,
                        IsCached = isCached
                    };
                    //if (result != null && result?.Count > 0)
                    //{
                    //    _masterService.PopulateActionsTaken(result, prebookRequest);
                    //}
                    var extraClientConfig = _clientPersistance.LoadRePricerDetail(usersData.RepricerUserId).GetAwaiter().GetResult();
                    var currency = extraClientConfig?.ExtraClientDetail?.Currency ?? "EUR";
                    var isAnyOtherConversionNeeded = reservationReportResponse.Data
                            .Any(
                                x => x?.Reservation?.CancellationPoliciesBySource?
                                        .Any(y => y?.Currency != currency) == true
                                    || x?.Prebook?.CancellationPoliciesBySource?
                                        .Any(y => y?.Currency != currency) == true
                                    || x?.Prebooks?.Any(p => p?.CancellationPoliciesBySource?
                                        .Any(y => y?.Currency != currency) == true) == true
                                ) == true ? true : false;
                    if (currency != "EUR" || isAnyOtherConversionNeeded)
                    {
                        try
                        {
                            var factor = _searchServicerHelper.RoundToDecimalPlaces(_exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, "EUR", currency));
                            var factorOriginal = _exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, currency, "EUR");
                            var cachedDataCopy = SerializeDeSerializeHelper.DeSerialize<ReservationReportResponse>(SerializeDeSerializeHelper.Serialize(reservationReportResponse));
                            reservationReportResponse = _masterService.CalculatedReservationReport(currency, factor, cachedDataCopy, factorOriginal);
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(GetRepricerReport)
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }
                    return GetResponseWithActionResult(reservationReportResponse);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetRepricerReport)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        /// <summary>
        /// Get data for client dashboard
        /// </summary>
        /// <param name="reservationReportRequest"></param>
        /// <returns></returns>
        [Route("GetRepricerReportSummary")]
        [HttpPost]
        [SwaggerResponse(200, "PreBookData", typeof(ReservationReportCalculation))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetRepricerReportSummary(DashboardSummaryRequest? reservationReportRequest)
        {
            var isCached = (reservationReportRequest?.isCached ?? true) == true;
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (reservationReportRequest?.RepricerId != 0)
                    {
                        reservationReportRequest.RepricerId = usersData.RepricerUserId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (reservationReportRequest?.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reservationReportRequest.RepricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var result = _masterPersistence.ReservationReportSummary(reservationReportRequest, isCached);
                    if (!isCached)
                    {
                        _masterService.GetReservationReport(new RepricerReportRequest
                        {
                            RepricerId = reservationReportRequest.RepricerId,
                            IsCached = isCached
                        });
                    }
                    var extraClientConfig = _clientPersistance.LoadRePricerDetail(usersData.RepricerUserId).GetAwaiter().GetResult();
                    var currency = extraClientConfig?.ExtraClientDetail?.Currency ?? "EUR";
                    if (currency != "EUR")
                    {
                        try
                        {
                            var factor = _exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, "EUR", currency);
                            var factorOriginal = _exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, currency, "EUR");
                            var cachedDataCopy = SerializeDeSerializeHelper.DeSerialize<ReservationReportCalculation>(SerializeDeSerializeHelper.Serialize(result));
                            result = _masterService.CalculatedReservationReportSummary(currency, factor, cachedDataCopy, factorOriginal);
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(GetRepricerReportSummary)
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }
                    return GetResponseWithActionResult(result);
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    var report = new ReservationReportCalculation();

                    report = _masterService.SuperAdminReservationReportSummary(reservationReportRequest);

                    return GetResponseWithActionResult(report);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetRepricerReportSummary)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        /// <summary>
        /// Get summary data for Super admin dashboard.
        /// </summary>
        /// <param name="reservationReportRequest"></param>
        /// <returns></returns>
        [Route("GetRepricerReportSummaryNew")]
        [HttpPost]
        [SwaggerResponse(200, "PreBookData", typeof(List<ReservationReportCalculations>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetRepricerReportSummaryNew(DashboardSummaryRequest? reservationReportRequest)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (reservationReportRequest?.RepricerId != 0)
                    {
                        reservationReportRequest.RepricerId = usersData.RepricerUserId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (reservationReportRequest?.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reservationReportRequest.RepricerId;
                    }
                }

                if (usersData.RepricerUserId != 0)
                {
                    var result = _masterPersistence.ReservationReportSummary(reservationReportRequest);
                    var extraClientConfig = _clientPersistance.LoadRePricerDetail(usersData.RepricerUserId).GetAwaiter().GetResult();
                    var currency = extraClientConfig?.ExtraClientDetail?.Currency ?? "EUR";

                    if (currency != "EUR")
                    {
                        try
                        {
                            var factor = _exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, "EUR", currency);
                            var factorOriginal = _exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, currency, "EUR");
                            var cachedDataCopy = SerializeDeSerializeHelper.DeSerialize<ReservationReportCalculation>(SerializeDeSerializeHelper.Serialize(result));
                            result = _masterService.CalculatedReservationReportSummary(currency, factor, cachedDataCopy, factorOriginal);
                        }
                        catch (Exception ex)
                        {
                            var irixErrorEntity = new IrixErrorEntity
                            {
                                ClassName = _className,
                                MethodName = nameof(GetRepricerReportSummary)
                            };
                            _log.Error(irixErrorEntity, ex);
                        }
                    }

                    // Create the ReservationReportCalculations object
                    var reportCalculations = new ReservationReportCalculations
                    {
                        ReportSummary = new List<ReservationReportCalculation> { result }
                    };
                    return GetResponseWithActionResult(reportCalculations);
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (reservationReportRequest != null)
                    {
                        reservationReportRequest.preBookFromDate = null;
                        reservationReportRequest.FirstCreatedFromDate = null;
                        reservationReportRequest.preBookToDate = null;
                        reservationReportRequest.FirstCreatedToDate = null;
                    }
                    var report = _masterService.SuperAdminReservationReport_SummaryNew(reservationReportRequest)
                        ?.OrderByDescending(x => x.RealizedGain)?.ToList()
                        ;

                    return GetResponseWithActionResult(report);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetRepricerReportSummaryNew)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        /// <summary>
        /// To Get Date Wise Count By RepricerId
        /// </summary>
        /// <param name="dateWiseRequest"></param>
        /// <returns></returns>
        [Route("GetDateWiseReservationCount")]
        [HttpPost]
        [SwaggerResponse(200, "PreBookData", typeof(DateRangeCountResponse))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetDateWiseReservationCount(DateWiseRequest dateWiseRequest)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (dateWiseRequest.RepricerId != 0)
                    {
                        usersData.RepricerUserId = dateWiseRequest.RepricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (dateWiseRequest.RepricerId != 0)
                    {
                        usersData.RepricerUserId = dateWiseRequest.RepricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var results = _masterPersistence.DateWiseCount(dateWiseRequest);
                    var dateRangeCountResponse = new DateRangeCountResponse();

                    if (results.Any())
                    {
                        dateRangeCountResponse.StartDate = results.First().Date;
                        dateRangeCountResponse.EndDate = results.Last().Date;
                        dateRangeCountResponse.Count = new List<int>();

                        foreach (var result in results)
                        {
                            dateRangeCountResponse.Count.Add(result.Count);
                        }
                    }

                    return GetResponseWithActionResult(dateRangeCountResponse);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetDateWiseReservationCount)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        /// <summary>
        /// To Get All TimeZonesDefined in Repricer
        /// </summary>
        /// <returns></returns>
        [Route("GetTimeZones")]
        [HttpGet]
        [SwaggerResponse(200, "Repricer Detail", typeof(List<TimeZones>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetTimeZones()
        {
            try
            {
                var result = (_clientPersistance.GetTimeZones()).GetAwaiter().GetResult();
                return GetResponseWithActionResult(result);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetTimeZones)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// Get optimized reservation invoice
        /// </summary>
        /// <param name="reservationRequestReport"> </param>
        /// <returns> </returns>
        [Route("GetInvoiceReport")]
        [HttpPost]
        [SwaggerResponse(200, "RepricerReportResponse", typeof(RepricerInvoiceResponse))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetInvoiceReport(CommonReportRequest reservationRequestReport)
        {
            try
            {
                //##todo remove these 2 line
                //reservationRequestReport.ToDate = "2025-04-30";
                //reservationRequestReport.FromDate = "2025-04-01";

                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (reservationRequestReport.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reservationRequestReport.RepricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (reservationRequestReport?.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reservationRequestReport.RepricerId;
                    }
                }

                if (usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User"))
                {
                    reservationRequestReport.IsCached = false;
                }

                var reservationReportResponse = _masterService.GetInvoiceData(reservationRequestReport);
                RepricerInvoiceResponse result = new RepricerInvoiceResponse
                {
                    ReservationReports = reservationReportResponse.ReservationReports,
                };
                var extraClientConfig = _clientPersistance.LoadRePricerDetail(usersData.RepricerUserId).GetAwaiter().GetResult();
                var currency = extraClientConfig?.ExtraClientDetail?.Currency ?? "EUR";
                if (currency != "EUR")
                {
                    try
                    {
                        var factor = _exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, "EUR", currency);
                        var factorOriginal = _exchangeRateService.ExchangeRateFactor(usersData.RepricerUserId, currency, "EUR");
                        var cachedDataCopy = SerializeDeSerializeHelper.DeSerialize<RepricerInvoiceResponse>(SerializeDeSerializeHelper.Serialize(result));
                        result = _masterService.GetRepricerInvoiceResponse(currency, factor, cachedDataCopy, factorOriginal);
                    }
                    catch (Exception ex)
                    {
                        var irixErrorEntity = new IrixErrorEntity
                        {
                            ClassName = _className,
                            MethodName = nameof(GetRepricerReport)
                        };
                        _log.Error(irixErrorEntity, ex);
                    }
                }
                return GetResponseWithActionResult(result);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetInvoiceReport)
                };
                _log.Error(irixErrorEntity, ex);
                return BadRequest(new
                {
                    Errors = ex.Message
                });
            }
        }

        private UserMapping GetRolesAndRepricerUserId()
        {
            var identity = HttpContext.User.Identity as ClaimsIdentity;
            var roles = new List<string>();
            int repricerUserId = 0;
            string userId = null; // Add userId variable
            string username = null;

            if (identity != null)
            {
                var userClaims = identity.Claims;

                roles = userClaims.Where(c => c.Type == ClaimTypes.Role)
                                 .Select(c => c.Value)
                                 .ToList();

                var repricerUserIdClaim = userClaims.FirstOrDefault(c => c.Type == "RePricerId")
                    ?? userClaims.FirstOrDefault(c => c.Type == "RePricerUserId")
                    ;
                var usernameClaim = userClaims.FirstOrDefault(c => c.Type == ClaimTypes.Name);
                username = usernameClaim?.Value;

                if (repricerUserIdClaim != null)
                {
                    int.TryParse(repricerUserIdClaim.Value, out repricerUserId);
                }

                var userIdClaim = userClaims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
                if (userIdClaim != null)
                {
                    userId = userIdClaim.Value;
                }
            }

            return new UserMapping
            {
                Roles = roles,
                RepricerUserId = repricerUserId,
                UserId = userId, // Add UserId to the UserMapping object
                RepricerUserName = username
            };
        }

        private void CacheRefreshAllUser()
        {
            try
            {
                var userdata = GetRolesAndRepricerUserId();
                if (userdata != null)
                {
                    var helper = new IdentityModelHelper(_userManager, _clientPersistance, _masterPersistence);
                    var usersList = helper.CacheRefreshAllUser(userdata);
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(CacheRefreshAllUser),
                };
            }
        }

        private RePricerDetail ValidateRePricerDetail(RePricerDetail rePricerDetail)
        {
            try
            {
                if (rePricerDetail.OptimizationType == OptimizationType.Undefined)
                {
                    rePricerDetail.OptimizationType = OptimizationType.Demo;
                }
                if (string.IsNullOrEmpty(rePricerDetail.ExtraClientDetail.Currency))
                {
                    rePricerDetail.ExtraClientDetail.Currency = "EUR";
                }
                rePricerDetail.ExtraClientDetail.IsUsePercentage = false;
                rePricerDetail.ExtraClientDetail.IsCreatePrebookForPriceEdgeCase = true;
                rePricerDetail.ExtraClientDetail.IsUseDaysLimitCancellationPolicyEdgeCase = true;
                rePricerDetail.ExtraClientDetail.PriceDifferencePercentage = 0;

                if (rePricerDetail.ClientConfigScheduler == null)
                {
                    rePricerDetail.ClientConfigScheduler.Reservation_CronTime = "10 2 * * *";
                    rePricerDetail.ClientConfigScheduler.PreBook_CronTime = "5 5 * * *";
                    rePricerDetail.ClientConfigScheduler.CurrencyExchange_CronTime = "0 12 * * *";
                }
                else if (rePricerDetail.ClientConfigScheduler.Reservation_CronTime == null)
                {
                    rePricerDetail.ClientConfigScheduler.Reservation_CronTime = "10 2 * * *";
                }
                if (rePricerDetail.ClientConfigScheduler.PreBook_CronTime == null)
                {
                    rePricerDetail.ClientConfigScheduler.PreBook_CronTime = "5 5 * * *";
                }
                if (rePricerDetail.ClientConfigScheduler.CurrencyExchange_CronTime == null)
                {
                    rePricerDetail.ClientConfigScheduler.CurrencyExchange_CronTime = "0 12 * * *";
                }
                if (rePricerDetail.ExtraClientDetail.PriceDifferenceValue == 0.0m)
                {
                    rePricerDetail.ExtraClientDetail.PriceDifferenceValue = 20.0m;
                }

                // Set default values for new properties if not specified
                if (rePricerDetail.ExtraClientDetail.PriceDifferenceValueAllowedBufferPercentage == 0)
                {
                    rePricerDetail.ExtraClientDetail.PriceDifferenceValueAllowedBufferPercentage = 0;
                }

                // Default value for IsCheckCrossSupperBeforeOptimization is false
                // No need to set explicitly as bool defaults to false
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(ValidateRePricerDetail),
                };
            }
            return rePricerDetail;
        }

        /// <summary>
        /// To Get All Different Currency by RepricerId
        /// </summary>
        /// <param name="RePricerId"></param>
        /// <returns></returns>
        [Route("GetCurrency/{RePricerId}")]
        [HttpGet]
        [SwaggerResponse(200, "Repricer Detail", typeof(List<string>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetCurrency(int RePricerId)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var result = (_reservationPersistance.GetDistinctCurrencyByRepricerId(RePricerId)).GetAwaiter().GetResult();
                    return GetResponseWithActionResult(result);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(CacheRefreshAllUser),
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        [HttpPost("lock/{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> LockUserById(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var lockoutEndDate = new DateTime(9999, 12, 31);
            var result = await _userManager.SetLockoutEndDateAsync(user, lockoutEndDate);

            if (!result.Succeeded)
            {
                return BadRequest(result.Errors);
            }

            return NoContent();
        }

        [HttpPost("unlock/{id}")]
        [Authorize(Roles = "SuperAdmin")]
        public async Task<IActionResult> UnlockUserById(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var result = await _userManager.SetLockoutEndDateAsync(user, DateTimeOffset.Now);

            if (!result.Succeeded)
            {
                return BadRequest(result.Errors);
            }

            return NoContent();
        }

        /// <summary>
        /// To UpdateRepricerStatus - Activate or Deactivate
        /// </summary>
        /// <param name="RePricerUserId"> </param>
        /// <param name="isActive"> true - active, false - inactive </param>
        /// <returns> </returns>
        [Route("UpdateRepricerStatus/{RePricerId}")]
        [HttpPut]
        [Authorize(Roles = "SuperAdmin")]
        public IActionResult UpdateRepricerStatus(int RePricerId, bool isActive = true)
        {
            try
            {
                var result = _clientPersistance.UpdateRepricerStatus(RePricerId, isActive);
                CacheRefreshAllUser();
                string response;
                if (result > 0)
                {
                    if (isActive)
                    {
                        response = "Activated Successfully";
                    }
                    else
                    {
                        response = "DeActivated Successfully";
                    }
                }
                else
                {
                    response = "Some Error Occured";
                }
                return GetResponseWithActionResult(response);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(DeleteRepricer)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        [HttpGet("GetSupplierCount")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<ActionResult<List<SupplierCountResponse>>> GetSupplierCount(int RePricerId, bool isCacheRefresh)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if (usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User"))
                {
                    RePricerId = usersData.RepricerUserId;
                }

                var result = _reservationPersistance.GetSupplierCount(RePricerId, isCacheRefresh);
                if (result == null || result.SupplierCounts == null || result.SupplierCounts.Count == 0)
                {
                    return NotFound(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    message = ex.Message
                });
            }
        }

        /// <summary>
        /// It gives summary  processed data that out of total searches that happened using GIATA Mappings and room board mappings
        /// </summary>
        /// <param name="RepricerId"></param>
        /// <param name="createdFromDate"></param>
        /// <param name="createdToDate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetMultiSupplierReservationReport")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public ActionResult<MultiSupplierReport> GetMultiSupplierPrebookSummary(int RepricerId = 0, DateTime? createdFromDate = null, DateTime? createdToDate = null, bool isCached = true)
        {
            bool isCacheRefresh = !isCached;
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if (usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User"))
                {
                    RepricerId = usersData.RepricerUserId;
                }
                var report = _reservationPersistance.GetMultiSupplierPrebookSummary(RepricerId, createdFromDate, createdToDate, isCacheRefresh);

                if (report == null)
                {
                    return NotFound("No report found for the given criteria.");
                }

                return Ok(report);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, new
                {
                    message = ex.Message
                });
            }
        }

        /// <summary>
        /// Get BoardMappings or reload cache BoardMappings
        /// </summary>
        /// <param name="isCacheRefresh"></param>
        /// <returns></returns>
        [Route("BoardMappings")]
        [HttpGet]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        [Authorize(Roles = "SuperAdmin")]
        [SwaggerResponse(200, "Board Mappings", typeof(List<BoardMapping>))]
        public async Task<IActionResult> BoardMappings(bool? isCacheRefresh = false)
        {
            try
            {
                var isCacheRebuildNeeded = isCacheRefresh ?? false;
                List<BoardMapping> result = null;

                Console.WriteLine($"\n    _AllBoardMappingsAsync\t\tStarted\t\t{DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")}");
                result = await _masterService.GetAllBoardMappingsAsync(isCacheRebuildNeeded);
                Console.WriteLine($"    _AllBoardMappingsAsync\t\tEnded\t\t{DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")}");

                Console.WriteLine($"    _AllRoomDetailsAsync\t\tStarted\t\t{DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")}");
                await _masterService.GetAllRoomDetailsAsync(isCacheRebuildNeeded);
                Console.WriteLine($"    _AllRoomDetailsAsync\t\tEnded\t\t{DateTime.UtcNow.ToString("yyyy-MMM-dd HH:mm:ss")}\n");

                return Ok(result);
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(BoardMapping),
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// Get status of running job RunRepricerJobForPrebookAndOptimizations
        /// </summary>
        /// <param name="RepricerId"></param>
        /// <returns></returns>
        [HttpGet("MainJobStatus/{RepricerId}")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        [Authorize(Roles = "SuperAdmin, Admin")]
        public IActionResult MainJobStatus(int RepricerId)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if (usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User"))
                {
                    RepricerId = usersData.RepricerUserId;
                }

                var statusMessage = _jobService.MainJobStatus(RepricerId);
                return Ok(statusMessage);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Retrieves daily active tab and optimization reports based on filters.
        /// RepricerId
        /// ReportType
        /// FromDate
        /// ToDate
        /// IsCached
        /// </summary>
        /// <returns>List of DailyOptimizationReportModel</returns>
        [HttpPost]
        [Route("GetDailyOptimizationReport")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        [SwaggerResponse(200, "DailyOptimizationReport", typeof(List<DailyOptimizationReportModel>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public async Task<IActionResult> GetDailyOptimizationReport(RepricerReportRequest reportRequest
           )
        {
            try
            {
                if (reportRequest == null)
                {
                    reportRequest = new RepricerReportRequest();
                }

                int? repricerId = reportRequest?.RepricerId ?? 0;

                string? reportType = null;// reportRequest?.ReportType ?? "Prebook";

                DateTime? reportDateFrom = reportRequest?.FromDate == null ? null : System.Convert.ToDateTime(reportRequest?.FromDate);

                DateTime? reportDateTo = reportRequest?.ToDate == null ? null : System.Convert.ToDateTime(reportRequest.ToDate);

                bool isCacheRefresh = reportRequest?.IsCached == false;

                var usersData = GetRolesAndRepricerUserId();
                reportRequest.PageNumber = reportRequest.PageNumber ?? 1;
                reportRequest.PageSize = reportRequest?.PageSize ?? 100;

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (reportRequest.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reportRequest.RepricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (reportRequest?.RepricerId != 0)
                    {
                        usersData.RepricerUserId = reportRequest.RepricerId;
                    }
                }

                // Validate date range within service; optional here if desired
                if (reportDateFrom.HasValue && reportDateTo.HasValue)
                {
                    var daysDifference = (reportDateTo.Value - reportDateFrom.Value).TotalDays;
                    if (daysDifference > 7)
                        return BadRequest("The date range cannot exceed 7 days.");
                }

                var reports = await _masterService.GetDailyOptimizationReportAsync(
                    repricerId,
                    reportType,
                    reportDateFrom,
                    reportDateTo,
                    isCacheRefresh);

                return Ok(reports);
            }
            catch (ArgumentException argEx)
            {
                // Handle validation errors
                return BadRequest(argEx.Message);
            }
            catch (Exception ex)
            {
                // Log the exception (implementation depends on your logging setup)
                // _log.LogError(ex, "An error occurred while fetching reports.");

                return StatusCode(500, "An internal server error occurred.");
            }
        }

        /// <summary>
        /// To Get Hotel using RepricerId
        /// </summary>
        /// <param name="RePricerId"></param>
        /// <returns></returns>
        [Route("GetHotel")]
        [HttpPost]
        [SwaggerResponse(200, "Repricer Detail", typeof(List<HotelMasterModel>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetHotel(countryCity countryCity)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (countryCity.RePricerId != 0)
                    {
                        usersData.RepricerUserId = countryCity.RePricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (countryCity.RePricerId != 0)
                    {
                        usersData.RepricerUserId = countryCity.RePricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var result = (_reservationService.GetHotel(countryCity.RePricerId, countryCity.CountryId, countryCity.CityId, countryCity.CacheRefresh)).GetAwaiter().GetResult();
                    return GetResponseWithActionResult(result);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetReseller)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// To Get Country using RepricerId
        /// </summary>
        /// <param name="RePricerId"></param>
        /// <returns></returns>
        [Route("GetCountry/{RePricerId}")]
        [HttpGet]
        [SwaggerResponse(200, "Repricer Detail", typeof(List<CountryMasterModel>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetCountry(int RePricerId, bool CacheRefresh = false)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var result = (_reservationService.GetCountry(RePricerId, CacheRefresh));
                    return GetResponseWithActionResult(result);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetReseller)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }

        /// <summary>
        /// To Get City using RepricerId
        /// </summary>
        /// <param name="RePricerId"></param>
        /// <returns></returns>
        [Route("GetCity/{RePricerId}")]
        [HttpGet]
        [SwaggerResponse(200, "Repricer Detail", typeof(List<CityMaster>))]
        [SwaggerResponse(400, "Bad request", typeof(ErrorResponse))]
        [Authorize(Roles = "Admin,SuperAdmin,User")]
        public IActionResult GetCity(int RePricerId, int ContryId, bool CacheRefresh = false)
        {
            try
            {
                var usersData = GetRolesAndRepricerUserId();

                if ((usersData.Roles.Any(role => role == "Admin") || usersData.Roles.Any(role => role == "User")) && usersData.RepricerUserId == 0)
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                    return BadRequest("Please Contact Administrator As No Repricerconfig found");
                }
                else if (usersData.Roles.Any(role => role == "SuperAdmin"))
                {
                    if (RePricerId != 0)
                    {
                        usersData.RepricerUserId = RePricerId;
                    }
                }
                if (usersData.RepricerUserId != 0)
                {
                    var result = (_reservationService.GetCity(RePricerId, ContryId, CacheRefresh));
                    return GetResponseWithActionResult(result);
                }
                else
                {
                    return BadRequest("RepricerID cannot be null");
                }
            }
            catch (Exception ex)
            {
                var irixErrorEntity = new IrixErrorEntity
                {
                    ClassName = _className,
                    MethodName = nameof(GetReseller)
                };
                _log.Error(irixErrorEntity, ex);
                return StatusCode(500, "Internal Server Error");
            }
        }
    }
}